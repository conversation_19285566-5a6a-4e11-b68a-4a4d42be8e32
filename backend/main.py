"""
Ghidra AI Agent - Main Application Entry Point

This module provides the main FastAPI application for the Ghidra AI Agent,
integrating llama.cpp for local LLM inference with GhidraMCP for reverse engineering workflows.
"""

from fastapi import FastAPI, WebSocket, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn
import asyncio
import logging
from pathlib import Path

from .core.agent import GhidraAIAgent
from .core.config import Settings
from .api.routes import api_router
from .api.websocket import websocket_manager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize settings
settings = Settings()

# Create FastAPI app
app = FastAPI(
    title="Ghidra AI Agent",
    description="AI-powered reverse engineering assistant for Ghidra",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize AI agent
agent = GhidraAIAgent(settings)

# Set agent instance for API routes
from .api.routes import set_agent, api_router
set_agent(agent)

# Include API routes
app.include_router(api_router, prefix="/api/v1")

@app.on_event("startup")
async def startup_event():
    """Initialize the application on startup."""
    logger.info("Starting Ghidra AI Agent...")
    
    # Initialize the AI agent
    await agent.initialize()
    
    # Check if models directory exists
    models_dir = Path("../models")
    if not models_dir.exists():
        logger.warning("Models directory not found. Please download LLM models.")
    
    logger.info("Ghidra AI Agent started successfully!")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on application shutdown."""
    logger.info("Shutting down Ghidra AI Agent...")
    await agent.cleanup()

# Include API routes
app.include_router(api_router, prefix="/api/v1")

# WebSocket endpoint for real-time communication
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time AI agent communication."""
    from .api.websocket import websocket_manager
    await websocket_manager.connect(websocket, agent)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "agent_ready": agent.is_ready(),
        "version": "1.0.0"
    }

# Serve frontend static files (in production)
if settings.serve_frontend:
    app.mount("/", StaticFiles(directory="../frontend/build", html=True), name="frontend")

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info"
    )

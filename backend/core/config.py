"""
Configuration settings for the Ghidra AI Agent.
"""

from pydantic_settings import BaseSettings
from typing import List, Optional
from pathlib import Path
import os


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Server settings
    host: str = "127.0.0.1"
    port: int = 8000
    debug: bool = False
    serve_frontend: bool = True
    
    # CORS settings
    cors_origins: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]
    
    # AI Model settings
    model_path: Optional[str] = None
    model_name: str = "llama-2-7b-chat.gguf"
    max_tokens: int = 2048
    temperature: float = 0.7
    
    # LLama.cpp settings
    n_ctx: int = 4096
    n_threads: int = 4
    n_gpu_layers: int = 0  # Set > 0 if you have GPU support
    
    # Ghidra settings
    ghidra_path: Optional[str] = None
    ghidra_mcp_host: str = "127.0.0.1"
    ghidra_mcp_port: int = 3001
    ghidra_projects_dir: str = "ghidra_projects"

    # MCP settings
    mcp_server_timeout: int = 30
    
    # Logging
    log_level: str = "INFO"
    log_file: Optional[str] = None
    
    # Security
    api_key: Optional[str] = None
    
    class Config:
        env_file = ".env"
        env_prefix = "GHIDRA_AI_"
    
    @property
    def models_dir(self) -> Path:
        """Get the models directory path."""
        return Path(__file__).parent.parent.parent / "models"
    
    @property
    def full_model_path(self) -> Optional[Path]:
        """Get the full path to the model file."""
        if self.model_path:
            return Path(self.model_path)
        
        models_dir = self.models_dir
        if models_dir.exists():
            model_file = models_dir / self.model_name
            if model_file.exists():
                return model_file
        
        return None
    
    def validate_settings(self) -> List[str]:
        """Validate settings and return list of issues."""
        issues = []
        
        # Check if model exists
        if not self.full_model_path or not self.full_model_path.exists():
            issues.append(f"Model file not found: {self.model_name}")
        
        # Check Ghidra path if provided
        if self.ghidra_path and not Path(self.ghidra_path).exists():
            issues.append(f"Ghidra path not found: {self.ghidra_path}")
        
        return issues

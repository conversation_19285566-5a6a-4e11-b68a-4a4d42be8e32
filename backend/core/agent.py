"""
Core AI Agent for Ghidra integration.
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path

try:
    from llama_cpp import <PERSON>lama
except ImportError:
    Llama = None

from .config import Settings
from .ghidra_interface import GhidraInterface, GhidraConnectionError
from .prompts import PromptManager

logger = logging.getLogger(__name__)


class GhidraAIAgent:
    """Main AI agent for Ghidra reverse engineering tasks."""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.llm: Optional[Llama] = None
        self.ghidra_interface: Optional[GhidraInterface] = None
        self.prompt_manager = PromptManager()
        self._ready = False
        
    async def initialize(self):
        """Initialize the AI agent and its components."""
        logger.info("Initializing Ghidra AI Agent...")
        
        # Validate settings
        issues = self.settings.validate_settings()
        if issues:
            logger.warning(f"Configuration issues: {issues}")
        
        # Initialize LLM
        await self._initialize_llm()
        
        # Initialize Ghidra interface
        await self._initialize_ghidra_interface()
        
        self._ready = True
        logger.info("Ghidra AI Agent initialized successfully!")
    
    async def _initialize_llm(self):
        """Initialize the LLama.cpp model."""
        if Llama is None:
            logger.error("llama-cpp-python not installed. Please install it.")
            return
        
        model_path = self.settings.full_model_path
        if not model_path:
            logger.warning("No model found. Please download a model to the models/ directory.")
            return
        
        try:
            logger.info(f"Loading model: {model_path}")
            self.llm = Llama(
                model_path=str(model_path),
                n_ctx=self.settings.n_ctx,
                n_threads=self.settings.n_threads,
                n_gpu_layers=self.settings.n_gpu_layers,
                verbose=False
            )
            logger.info("LLM loaded successfully!")
        except Exception as e:
            logger.error(f"Failed to load LLM: {e}")
    
    async def _initialize_ghidra_interface(self):
        """Initialize the Ghidra MCP interface."""
        try:
            self.ghidra_interface = GhidraInterface(
                port=self.settings.ghidra_mcp_port,
                host=getattr(self.settings, 'ghidra_mcp_host', '127.0.0.1'),
                timeout=getattr(self.settings, 'mcp_server_timeout', 30)
            )
            await self.ghidra_interface.connect()
            logger.info("Ghidra interface connected!")
        except GhidraConnectionError as e:
            logger.warning(f"Ghidra interface connection failed: {e}")
            self.ghidra_interface = None
        except Exception as e:
            logger.error(f"Failed to connect to Ghidra interface: {e}")
            self.ghidra_interface = None
    
    def is_ready(self) -> bool:
        """Check if the agent is ready to process requests."""
        return self._ready and self.llm is not None
    
    async def analyze_binary(self, binary_path: str, analysis_type: str = "full") -> Dict[str, Any]:
        """Analyze a binary file using Ghidra and AI."""
        if not self.is_ready():
            raise RuntimeError("Agent not ready")
        
        logger.info(f"Starting analysis of {binary_path}")
        
        # Step 1: Load binary in Ghidra
        project_result = await self.ghidra_interface.create_project(binary_path)
        
        # Step 2: Run initial Ghidra analysis
        analysis_result = await self.ghidra_interface.analyze_binary(analysis_type)
        
        # Step 3: Get AI insights
        ai_insights = await self._get_ai_insights(analysis_result, analysis_type)
        
        return {
            "binary_path": binary_path,
            "project_info": project_result,
            "ghidra_analysis": analysis_result,
            "ai_insights": ai_insights,
            "status": "completed"
        }
    
    async def _get_ai_insights(self, ghidra_data: Dict[str, Any], analysis_type: str) -> Dict[str, Any]:
        """Generate AI insights from Ghidra analysis data."""
        if not self.llm:
            return {"error": "LLM not available"}
        
        # Create prompt based on analysis type
        prompt = self.prompt_manager.create_analysis_prompt(ghidra_data, analysis_type)
        
        try:
            # Generate AI response
            response = self.llm(
                prompt,
                max_tokens=self.settings.max_tokens,
                temperature=self.settings.temperature,
                stop=["</analysis>", "\n\n---"]
            )
            
            return {
                "insights": response["choices"][0]["text"].strip(),
                "model_used": self.settings.model_name,
                "tokens_used": response["usage"]["total_tokens"]
            }
        except Exception as e:
            logger.error(f"AI analysis failed: {e}")
            return {"error": str(e)}
    
    async def chat(self, message: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Chat interface for interactive AI assistance."""
        if not self.llm:
            return {"error": "LLM not available"}
        
        # Create chat prompt
        prompt = self.prompt_manager.create_chat_prompt(message, context)
        
        try:
            response = self.llm(
                prompt,
                max_tokens=512,
                temperature=0.8,
                stop=["Human:", "Assistant:"]
            )
            
            return {
                "response": response["choices"][0]["text"].strip(),
                "context": context
            }
        except Exception as e:
            logger.error(f"Chat failed: {e}")
            return {"error": str(e)}
    
    async def cleanup(self):
        """Cleanup resources."""
        if self.ghidra_interface:
            await self.ghidra_interface.disconnect()
        
        # LLama.cpp cleanup is handled automatically
        self._ready = False
        logger.info("Agent cleanup completed")

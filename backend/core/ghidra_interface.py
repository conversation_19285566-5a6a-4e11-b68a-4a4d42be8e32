"""
Ghidra MCP Interface for communication with GhidraMCP server.

This module provides the GhidraInterface class that handles HTTP communication
with the GhidraMCP server, following the Model Context Protocol for Ghidra integration.
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List
import aiohttp
import json
from pathlib import Path

logger = logging.getLogger(__name__)


class GhidraConnectionError(Exception):
    """Specific exception for MCP connection issues."""
    pass


class GhidraInterface:
    """Interface for communicating with GhidraMCP server."""

    def __init__(self, port: int = 3001, host: str = "127.0.0.1", timeout: int = 30):
        """
        Initialize the Ghidra MCP interface.

        Args:
            port: MCP server port (default: 3001)
            host: MCP server host (default: 127.0.0.1)
            timeout: Request timeout in seconds (default: 30)
        """
        self.host = host
        self.port = port
        self.timeout = timeout
        self.base_url = f"http://{host}:{port}"
        self.session: Optional[aiohttp.ClientSession] = None
        self._connected = False

    async def connect(self) -> None:
        """Establish connection to the GhidraMCP server."""
        try:
            logger.info(f"Connecting to GhidraMCP server at {self.base_url}")

            # Create aiohttp session with timeout
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self.session = aiohttp.ClientSession(timeout=timeout)

            # Test connection with health check
            await self._health_check()
            self._connected = True
            logger.info("GhidraMCP connection established successfully!")

        except Exception as e:
            logger.error(f"Failed to connect to GhidraMCP server: {e}")
            if self.session:
                await self.session.close()
                self.session = None
            raise GhidraConnectionError(f"Connection failed: {e}")

    async def disconnect(self) -> None:
        """Close connection to the GhidraMCP server."""
        if self.session:
            await self.session.close()
            self.session = None
        self._connected = False
        logger.info("GhidraMCP connection closed")

    async def _health_check(self) -> None:
        """Perform health check on the MCP server."""
        try:
            response = await self._make_request("GET", "/health")
            if response.get("status") != "healthy":
                raise GhidraConnectionError("MCP server health check failed")
        except Exception as e:
            raise GhidraConnectionError(f"Health check failed: {e}")

    async def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Make HTTP request to MCP server with retry logic.

        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint
            data: Request payload

        Returns:
            Response data as dictionary

        Raises:
            GhidraConnectionError: If request fails after retries
        """
        if not self.session:
            raise GhidraConnectionError("Not connected to MCP server")

        url = f"{self.base_url}{endpoint}"
        max_retries = 3

        for attempt in range(max_retries):
            try:
                if method.upper() == "GET":
                    async with self.session.get(url, params=data) as response:
                        return await self._handle_response(response)
                elif method.upper() == "POST":
                    async with self.session.post(url, json=data) as response:
                        return await self._handle_response(response)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")

            except aiohttp.ClientError as e:
                if attempt == max_retries - 1:
                    raise GhidraConnectionError(f"Request failed after {max_retries} attempts: {e}")

                # Exponential backoff
                wait_time = 2 ** attempt
                logger.warning(f"Request attempt {attempt + 1} failed, retrying in {wait_time}s: {e}")
                await asyncio.sleep(wait_time)

    async def _handle_response(self, response: aiohttp.ClientResponse) -> Dict[str, Any]:
        """Handle HTTP response and extract JSON data."""
        try:
            if response.status >= 400:
                error_text = await response.text()
                raise GhidraConnectionError(f"HTTP {response.status}: {error_text}")

            return await response.json()
        except json.JSONDecodeError as e:
            raise GhidraConnectionError(f"Invalid JSON response: {e}")

    async def create_project(self, binary_path: str, project_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Create a new Ghidra project and import binary.

        Args:
            binary_path: Path to the binary file
            project_name: Optional project name (defaults to binary filename)

        Returns:
            Project creation result
        """
        if not self._connected:
            raise GhidraConnectionError("Not connected to MCP server")

        if not project_name:
            project_name = Path(binary_path).stem

        logger.info(f"Creating Ghidra project '{project_name}' for binary: {binary_path}")

        try:
            data = {
                "binary_path": binary_path,
                "project_name": project_name
            }

            result = await self._make_request("POST", "/api/project/create", data)
            logger.info(f"Project created successfully: {result.get('project_id', 'unknown')}")
            return result

        except Exception as e:
            logger.error(f"Failed to create project: {e}")
            return {"error": str(e)}

    async def analyze_binary(self, analysis_type: str = "full") -> Dict[str, Any]:
        """
        Run Ghidra analysis on the current project.

        Args:
            analysis_type: Type of analysis to perform (full, quick, custom)

        Returns:
            Analysis results
        """
        if not self._connected:
            raise GhidraConnectionError("Not connected to MCP server")

        logger.info(f"Starting {analysis_type} analysis")

        try:
            data = {"analysis_type": analysis_type}
            result = await self._make_request("POST", "/api/analysis/run", data)
            logger.info("Analysis completed successfully")
            return result

        except Exception as e:
            logger.error(f"Analysis failed: {e}")
            return {"error": str(e)}

    async def get_functions(self) -> Dict[str, Any]:
        """
        Retrieve list of functions from current project.

        Returns:
            Function list and metadata
        """
        if not self._connected:
            raise GhidraConnectionError("Not connected to MCP server")

        try:
            result = await self._make_request("GET", "/api/functions/list")
            logger.info(f"Retrieved {len(result.get('functions', []))} functions")
            return result

        except Exception as e:
            logger.error(f"Failed to get functions: {e}")
            return {"error": str(e)}

    async def decompile_function(self, address: str) -> Dict[str, Any]:
        """
        Decompile a specific function.

        Args:
            address: Function address (hex string)

        Returns:
            Decompiled function code and metadata
        """
        if not self._connected:
            raise GhidraConnectionError("Not connected to MCP server")

        logger.info(f"Decompiling function at address: {address}")

        try:
            data = {"address": address}
            result = await self._make_request("POST", "/api/function/decompile", data)
            logger.info("Function decompiled successfully")
            return result

        except Exception as e:
            logger.error(f"Failed to decompile function: {e}")
            return {"error": str(e)}

    def is_connected(self) -> bool:
        """Check if connected to MCP server."""
        return self._connected and self.session is not None

    async def __aenter__(self):
        """Async context manager entry."""
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.disconnect()
"""
WebSocket manager for real-time communication with the Ghidra AI Agent.

This module provides the WebSocketManager class that handles WebSocket connections,
message routing, and real-time updates for analysis progress and chat interactions.
"""

import logging
import json
import asyncio
from typing import Dict, List, Any, Optional
from fastapi import WebSocket, WebSocketDisconnect
import uuid

logger = logging.getLogger(__name__)


class WebSocketManager:
    """Manager for WebSocket connections and real-time communication."""

    def __init__(self):
        """Initialize the WebSocket manager."""
        self.active_connections: Dict[str, WebSocket] = {}
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}

    async def connect(self, websocket: WebSocket, agent) -> str:
        """
        Accept a new WebSocket connection.

        Args:
            websocket: WebSocket connection
            agent: Agent instance for handling requests

        Returns:
            Connection ID
        """
        await websocket.accept()

        # Generate unique connection ID
        connection_id = str(uuid.uuid4())

        # Store connection
        self.active_connections[connection_id] = websocket
        self.connection_metadata[connection_id] = {
            "connected_at": asyncio.get_event_loop().time(),
            "agent": agent
        }

        logger.info(f"WebSocket connection established: {connection_id}")

        # Send welcome message
        await self.send_message(websocket, {
            "type": "connection",
            "status": "connected",
            "connection_id": connection_id,
            "agent_ready": agent.is_ready()
        })

        # Start message handling loop
        try:
            await self._handle_connection(websocket, connection_id, agent)
        except WebSocketDisconnect:
            await self.disconnect(connection_id)
        except Exception as e:
            logger.error(f"WebSocket error for {connection_id}: {e}")
            await self.disconnect(connection_id)

        return connection_id

    async def disconnect(self, connection_id: str):
        """
        Disconnect a WebSocket connection.

        Args:
            connection_id: Connection ID to disconnect
        """
        if connection_id in self.active_connections:
            try:
                websocket = self.active_connections[connection_id]
                await websocket.close()
            except Exception as e:
                logger.warning(f"Error closing WebSocket {connection_id}: {e}")

            del self.active_connections[connection_id]
            del self.connection_metadata[connection_id]

            logger.info(f"WebSocket connection closed: {connection_id}")

    async def send_message(self, websocket: WebSocket, message: Dict[str, Any]):
        """
        Send a message to a specific WebSocket connection.

        Args:
            websocket: WebSocket connection
            message: Message to send
        """
        try:
            await websocket.send_text(json.dumps(message))
        except Exception as e:
            logger.error(f"Failed to send WebSocket message: {e}")

    async def broadcast(self, message: Dict[str, Any], exclude: Optional[List[str]] = None):
        """
        Broadcast a message to all active connections.

        Args:
            message: Message to broadcast
            exclude: List of connection IDs to exclude
        """
        exclude = exclude or []

        for connection_id, websocket in self.active_connections.items():
            if connection_id not in exclude:
                await self.send_message(websocket, message)

    async def _handle_connection(self, websocket: WebSocket, connection_id: str, agent):
        """
        Handle messages from a WebSocket connection.

        Args:
            websocket: WebSocket connection
            connection_id: Connection ID
            agent: Agent instance
        """
        while True:
            try:
                # Receive message
                data = await websocket.receive_text()
                message = json.loads(data)

                # Handle message
                await self._handle_message(websocket, connection_id, message, agent)

            except WebSocketDisconnect:
                logger.info(f"WebSocket {connection_id} disconnected")
                break
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON from {connection_id}: {e}")
                await self.send_message(websocket, {
                    "type": "error",
                    "message": "Invalid JSON format"
                })
            except Exception as e:
                logger.error(f"Error handling message from {connection_id}: {e}")
                await self.send_message(websocket, {
                    "type": "error",
                    "message": f"Message handling error: {str(e)}"
                })

    async def _handle_message(self, websocket: WebSocket, connection_id: str, message: Dict[str, Any], agent):
        """
        Handle a specific message from a WebSocket connection.

        Args:
            websocket: WebSocket connection
            connection_id: Connection ID
            message: Received message
            agent: Agent instance
        """
        message_type = message.get("type")

        logger.debug(f"Handling message type '{message_type}' from {connection_id}")

        try:
            if message_type == "ping":
                await self._handle_ping(websocket, message)

            elif message_type == "chat":
                await self._handle_chat(websocket, message, agent)

            elif message_type == "analysis_request":
                await self._handle_analysis_request(websocket, message, agent)

            elif message_type == "status_request":
                await self._handle_status_request(websocket, agent)

            elif message_type == "function_analysis":
                await self._handle_function_analysis(websocket, message, agent)

            else:
                await self.send_message(websocket, {
                    "type": "error",
                    "message": f"Unknown message type: {message_type}"
                })

        except Exception as e:
            logger.error(f"Error handling {message_type} message: {e}")
            await self.send_message(websocket, {
                "type": "error",
                "message": f"Error processing {message_type}: {str(e)}"
            })

    async def _handle_ping(self, websocket: WebSocket, message: Dict[str, Any]):
        """Handle ping message."""
        await self.send_message(websocket, {
            "type": "pong",
            "timestamp": message.get("timestamp")
        })

    async def _handle_chat(self, websocket: WebSocket, message: Dict[str, Any], agent):
        """Handle chat message."""
        user_message = message.get("message", "")
        context = message.get("context")

        if not user_message:
            await self.send_message(websocket, {
                "type": "error",
                "message": "Empty chat message"
            })
            return

        # Send typing indicator
        await self.send_message(websocket, {
            "type": "chat_typing",
            "status": "typing"
        })

        try:
            # Get AI response
            result = await agent.chat(message=user_message, context=context)

            # Send response
            await self.send_message(websocket, {
                "type": "chat_response",
                "response": result.get("response", ""),
                "context": result.get("context"),
                "error": result.get("error")
            })

        except Exception as e:
            await self.send_message(websocket, {
                "type": "chat_response",
                "error": f"Chat failed: {str(e)}"
            })
        finally:
            # Stop typing indicator
            await self.send_message(websocket, {
                "type": "chat_typing",
                "status": "stopped"
            })

    async def _handle_analysis_request(self, websocket: WebSocket, message: Dict[str, Any], agent):
        """Handle binary analysis request."""
        binary_path = message.get("binary_path")
        analysis_type = message.get("analysis_type", "full")

        if not binary_path:
            await self.send_message(websocket, {
                "type": "error",
                "message": "Binary path required"
            })
            return

        # Send analysis started notification
        await self.send_message(websocket, {
            "type": "analysis_status",
            "status": "started",
            "binary_path": binary_path,
            "analysis_type": analysis_type
        })

        try:
            # Perform analysis with progress updates
            result = await self._analyze_with_progress(websocket, agent, binary_path, analysis_type)

            # Send final result
            await self.send_message(websocket, {
                "type": "analysis_complete",
                "result": result
            })

        except Exception as e:
            await self.send_message(websocket, {
                "type": "analysis_error",
                "error": f"Analysis failed: {str(e)}"
            })

    async def _handle_status_request(self, websocket: WebSocket, agent):
        """Handle status request."""
        ghidra_connected = False
        if agent.ghidra_interface:
            ghidra_connected = agent.ghidra_interface.is_connected()

        await self.send_message(websocket, {
            "type": "status_response",
            "agent_ready": agent.is_ready(),
            "ghidra_connected": ghidra_connected,
            "version": "1.0.0"
        })

    async def _handle_function_analysis(self, websocket: WebSocket, message: Dict[str, Any], agent):
        """Handle function analysis request."""
        address = message.get("address")

        if not address:
            await self.send_message(websocket, {
                "type": "error",
                "message": "Function address required"
            })
            return

        try:
            # Decompile function
            decompile_result = await agent.ghidra_interface.decompile_function(address)

            if "error" in decompile_result:
                await self.send_message(websocket, {
                    "type": "function_analysis_error",
                    "error": decompile_result["error"]
                })
                return

            # Generate AI insights
            ai_insights = await agent._get_ai_insights(decompile_result, "function")

            await self.send_message(websocket, {
                "type": "function_analysis_complete",
                "address": address,
                "decompile_result": decompile_result,
                "ai_insights": ai_insights
            })

        except Exception as e:
            await self.send_message(websocket, {
                "type": "function_analysis_error",
                "error": f"Function analysis failed: {str(e)}"
            })

    async def _analyze_with_progress(self, websocket: WebSocket, agent, binary_path: str, analysis_type: str):
        """
        Perform analysis with progress updates.

        Args:
            websocket: WebSocket connection for progress updates
            agent: Agent instance
            binary_path: Path to binary file
            analysis_type: Type of analysis

        Returns:
            Analysis result
        """
        # Step 1: Create project
        await self.send_message(websocket, {
            "type": "analysis_progress",
            "step": "creating_project",
            "progress": 25
        })

        project_result = await agent.ghidra_interface.create_project(binary_path)

        # Step 2: Run Ghidra analysis
        await self.send_message(websocket, {
            "type": "analysis_progress",
            "step": "ghidra_analysis",
            "progress": 50
        })

        analysis_result = await agent.ghidra_interface.analyze_binary(analysis_type)

        # Step 3: Generate AI insights
        await self.send_message(websocket, {
            "type": "analysis_progress",
            "step": "ai_analysis",
            "progress": 75
        })

        ai_insights = await agent._get_ai_insights(analysis_result, analysis_type)

        # Complete
        await self.send_message(websocket, {
            "type": "analysis_progress",
            "step": "complete",
            "progress": 100
        })

        return {
            "binary_path": binary_path,
            "project_info": project_result,
            "ghidra_analysis": analysis_result,
            "ai_insights": ai_insights,
            "status": "completed"
        }

    def get_connection_count(self) -> int:
        """Get the number of active connections."""
        return len(self.active_connections)

    def get_connection_info(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all active connections."""
        return {
            conn_id: {
                "connected_at": metadata["connected_at"],
                "agent_ready": metadata["agent"].is_ready()
            }
            for conn_id, metadata in self.connection_metadata.items()
        }


# Global WebSocket manager instance
websocket_manager = WebSocketManager()
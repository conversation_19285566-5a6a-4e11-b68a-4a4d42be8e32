"""
FastAPI routes for the Ghidra AI Agent.

This module provides RESTful API endpoints for binary analysis, project management,
chat interactions, and system status.
"""

import logging
from typing import Dict, Any, Optional, List
from pathlib import Path
import tempfile
import os

from fastapi import APIRouter, HTTPException, UploadFile, File, Depends
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

# Create API router
api_router = APIRouter()

# Global agent instance (will be set by main.py)
_agent_instance = None


def get_agent():
    """Dependency to get the agent instance."""
    if _agent_instance is None:
        raise HTTPException(status_code=503, detail="Agent not initialized")
    return _agent_instance


def set_agent(agent):
    """Set the global agent instance."""
    global _agent_instance
    _agent_instance = agent


# Pydantic models for request/response validation
class AnalysisRequest(BaseModel):
    """Request model for binary analysis."""
    binary_path: str = Field(..., description="Path to the binary file")
    analysis_type: str = Field(default="full", description="Type of analysis (full, quick, security)")
    project_name: Optional[str] = Field(None, description="Optional project name")


class ChatRequest(BaseModel):
    """Request model for chat interactions."""
    message: str = Field(..., description="User message")
    context: Optional[Dict[str, Any]] = Field(None, description="Optional context from previous interactions")


class FunctionAnalysisRequest(BaseModel):
    """Request model for function analysis."""
    address: str = Field(..., description="Function address (hex string)")


class AnalysisResponse(BaseModel):
    """Response model for analysis results."""
    status: str
    binary_path: Optional[str] = None
    project_info: Optional[Dict[str, Any]] = None
    ghidra_analysis: Optional[Dict[str, Any]] = None
    ai_insights: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class ChatResponse(BaseModel):
    """Response model for chat interactions."""
    response: str
    context: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class StatusResponse(BaseModel):
    """Response model for system status."""
    status: str
    agent_ready: bool
    ghidra_connected: bool
    version: str


class ProjectListResponse(BaseModel):
    """Response model for project list."""
    projects: List[Dict[str, Any]]
    total: int


@api_router.post("/analyze", response_model=AnalysisResponse)
async def analyze_binary(request: AnalysisRequest, agent=Depends(get_agent)):
    """
    Analyze a binary file using Ghidra and AI.

    Args:
        request: Analysis request parameters
        agent: Agent instance dependency

    Returns:
        Analysis results including Ghidra data and AI insights
    """
    try:
        logger.info(f"Starting analysis of {request.binary_path}")

        # Check if agent is ready
        if not agent.is_ready():
            raise HTTPException(status_code=503, detail="Agent not ready")

        # Validate binary path
        binary_path = Path(request.binary_path)
        if not binary_path.exists():
            raise HTTPException(status_code=404, detail=f"Binary file not found: {request.binary_path}")

        # Perform analysis
        result = await agent.analyze_binary(
            binary_path=str(binary_path),
            analysis_type=request.analysis_type
        )

        return AnalysisResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")


@api_router.post("/upload")
async def upload_binary(file: UploadFile = File(...)):
    """
    Upload a binary file for analysis.

    Args:
        file: Uploaded binary file

    Returns:
        File upload result with path
    """
    try:
        # Validate file
        if not file.filename:
            raise HTTPException(status_code=400, detail="No file provided")

        # Create temporary file
        temp_dir = Path(tempfile.gettempdir()) / "ghidra_ai_agent"
        temp_dir.mkdir(exist_ok=True)

        file_path = temp_dir / file.filename

        # Save uploaded file
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        logger.info(f"File uploaded: {file_path}")

        return {
            "status": "success",
            "filename": file.filename,
            "path": str(file_path),
            "size": len(content)
        }

    except Exception as e:
        logger.error(f"File upload failed: {e}")
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")


@api_router.get("/projects", response_model=ProjectListResponse)
async def list_projects(agent=Depends(get_agent)):
    """
    List available Ghidra projects.

    Args:
        agent: Agent instance dependency

    Returns:
        List of available projects
    """
    try:
        # This would typically query the Ghidra interface for projects
        # For now, return a placeholder response
        projects = []

        if agent.ghidra_interface and agent.ghidra_interface.is_connected():
            # In a real implementation, this would query Ghidra for projects
            projects = [
                {
                    "name": "example_project",
                    "created": "2024-01-01T00:00:00Z",
                    "binary": "example.exe",
                    "status": "analyzed"
                }
            ]

        return ProjectListResponse(projects=projects, total=len(projects))

    except Exception as e:
        logger.error(f"Failed to list projects: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list projects: {str(e)}")


@api_router.post("/chat", response_model=ChatResponse)
async def chat_with_agent(request: ChatRequest, agent=Depends(get_agent)):
    """
    Chat with the AI agent for interactive assistance.

    Args:
        request: Chat request with message and context
        agent: Agent instance dependency

    Returns:
        AI response with updated context
    """
    try:
        logger.info(f"Chat request: {request.message[:100]}...")

        # Check if agent is ready
        if not agent.is_ready():
            raise HTTPException(status_code=503, detail="Agent not ready")

        # Get AI response
        result = await agent.chat(
            message=request.message,
            context=request.context
        )

        return ChatResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Chat failed: {e}")
        raise HTTPException(status_code=500, detail=f"Chat failed: {str(e)}")


@api_router.post("/function/analyze")
async def analyze_function(request: FunctionAnalysisRequest, agent=Depends(get_agent)):
    """
    Analyze a specific function.

    Args:
        request: Function analysis request
        agent: Agent instance dependency

    Returns:
        Function analysis results
    """
    try:
        logger.info(f"Analyzing function at address: {request.address}")

        # Check if agent is ready
        if not agent.is_ready():
            raise HTTPException(status_code=503, detail="Agent not ready")

        # Check if Ghidra interface is connected
        if not agent.ghidra_interface or not agent.ghidra_interface.is_connected():
            raise HTTPException(status_code=503, detail="Ghidra interface not connected")

        # Decompile function
        decompile_result = await agent.ghidra_interface.decompile_function(request.address)

        if "error" in decompile_result:
            raise HTTPException(status_code=400, detail=decompile_result["error"])

        # Generate AI insights for the function
        ai_insights = await agent._get_ai_insights(decompile_result, "function")

        return {
            "status": "success",
            "address": request.address,
            "decompile_result": decompile_result,
            "ai_insights": ai_insights
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Function analysis failed: {e}")
        raise HTTPException(status_code=500, detail=f"Function analysis failed: {str(e)}")


@api_router.get("/status", response_model=StatusResponse)
async def get_status(agent=Depends(get_agent)):
    """
    Get system status and health information.

    Args:
        agent: Agent instance dependency

    Returns:
        System status information
    """
    try:
        ghidra_connected = False
        if agent.ghidra_interface:
            ghidra_connected = agent.ghidra_interface.is_connected()

        return StatusResponse(
            status="healthy" if agent.is_ready() else "not_ready",
            agent_ready=agent.is_ready(),
            ghidra_connected=ghidra_connected,
            version="1.0.0"
        )

    except Exception as e:
        logger.error(f"Status check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Status check failed: {str(e)}")


@api_router.get("/functions")
async def list_functions(agent=Depends(get_agent)):
    """
    List functions from the current Ghidra project.

    Args:
        agent: Agent instance dependency

    Returns:
        List of functions
    """
    try:
        # Check if agent is ready
        if not agent.is_ready():
            raise HTTPException(status_code=503, detail="Agent not ready")

        # Check if Ghidra interface is connected
        if not agent.ghidra_interface or not agent.ghidra_interface.is_connected():
            raise HTTPException(status_code=503, detail="Ghidra interface not connected")

        # Get functions from Ghidra
        result = await agent.ghidra_interface.get_functions()

        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to list functions: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list functions: {str(e)}")
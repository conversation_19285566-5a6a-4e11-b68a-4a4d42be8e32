# Ghidra AI Agent

An intelligent AI agent that combines the power of llama.cpp, Ghidra reverse engineering platform, and GhidraMCP server with a custom frontend for advanced binary analysis and reverse engineering tasks.

## Overview

This project creates an AI-powered assistant specifically tailored for reverse engineering workflows using <PERSON>hidra. The agent can understand and interact with <PERSON><PERSON><PERSON>'s capabilities through the GhidraMCP server, providing intelligent analysis, code suggestions, and automated reverse engineering tasks.

## Architecture

```
ghidra-ai-agent/
├── frontend/           # Custom web-based frontend for Ghidra integration
├── backend/           # Python backend with AI agent logic
├── llama-cpp/         # llama.cpp integration and model management
├── ghidra-mcp/        # GhidraMCP server integration
├── models/            # AI model storage
├── docs/              # Documentation and guides
└── scripts/           # Utility scripts and automation
```

## Components

### 1. Frontend (React + TypeScript)
- Custom UI tailored for Ghidra workflows
- Real-time communication with AI agent
- Visual representation of analysis results
- Integration with <PERSON><PERSON><PERSON>'s project structure
- Code viewer with AI-powered annotations

### 2. Backend (Python + FastAPI)
- AI agent orchestration
- llama.cpp integration for local LLM inference
- GhidraMCP server communication
- Analysis pipeline management
- WebSocket support for real-time updates

### 3. LLama.cpp Integration
- Local LLM inference for privacy and speed
- Custom prompts for reverse engineering tasks
- Model management and optimization

### 4. GhidraMCP Server
- Bridge between AI agent and Ghidra
- Automated script execution
- Project management
- Analysis result extraction

## Features

- **Intelligent Binary Analysis**: AI-powered analysis of binary files
- **Automated Reverse Engineering**: Streamlined workflows for common RE tasks
- **Custom Ghidra Integration**: Deep integration with Ghidra's capabilities
- **Local AI Processing**: Privacy-focused local LLM inference
- **Real-time Collaboration**: Live updates and analysis sharing
- **Extensible Architecture**: Plugin system for custom workflows## Requirements

- Python 3.8+
- Node.js 18+
- Ghidra 10.0+
- Linux/macOS (Windows support planned)
- 8GB+ RAM (16GB recommended for large models)

## Quick Start

1. **Clone and Setup**
   ```bash
   cd ghidra-ai-agent
   ./scripts/setup.sh
   ```

2. **Install Dependencies**
   ```bash
   # Backend dependencies
   cd backend && pip install -r requirements.txt
   
   # Frontend dependencies
   cd frontend && npm install
   ```

3. **Configure Ghidra**
   ```bash
   # Setup GhidraMCP server
   cd ghidra-mcp && ./setup.sh
   ```

4. **Start the Application**
   ```bash
   # Start backend
   cd backend && python main.py
   
   # Start frontend (new terminal)
   cd frontend && npm start
   ```

## Configuration

See `docs/configuration.md` for detailed configuration options.

## Contributing

See `docs/contributing.md` for contribution guidelines.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [llama.cpp](https://github.com/ggml-org/llama.cpp) - LLM inference engine
- [llama-cpp-python](https://github.com/abetlen/llama-cpp-python) - Python bindings
- [Ghidra](https://github.com/NationalSecurityAgency/ghidra) - Reverse engineering platform
- [GhidraMCP](https://github.com/LaurieWired/GhidraMCP) - MCP server for Ghidra